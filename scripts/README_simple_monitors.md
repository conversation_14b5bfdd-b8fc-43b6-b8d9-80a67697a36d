# 简化版 WebSocket 监控器使用说明

本目录包含了三个版本的 WebSocket 监控器，从复杂到简单：

## 📁 文件说明

### 1. `task_monitor.py` - 完整版（原始文件）
- ✅ 完整的生产环境功能
- ✅ 动态服务器管理
- ✅ 与现有系统集成
- ✅ 完整的错误处理和日志
- ❌ 代码复杂，依赖较多

### 2. `simple_websocket_monitor.py` - 简化版
- ✅ 保留核心功能
- ✅ 面向对象设计
- ✅ 多服务器支持
- ✅ 相对简单易懂
- ❌ 功能有所简化

### 3. `minimal_websocket_monitor.py` - 最简版
- ✅ 极简设计
- ✅ 函数式编程
- ✅ 易于理解和修改
- ✅ 最少依赖
- ❌ 功能最基础

## 🚀 快速开始

### 安装依赖
```bash
pip install websockets
```

### 使用最简版本
```python
import asyncio
from minimal_websocket_monitor import simple_websocket_client

# 监控单个服务器
asyncio.run(simple_websocket_client("*************:8188"))
```

### 使用简化版本
```python
import asyncio
from simple_websocket_monitor import SimpleWebSocketMonitor

async def main():
    monitor = SimpleWebSocketMonitor("*************:8188")
    await monitor.connect_and_monitor()

asyncio.run(main())
```

## 🔧 功能对比

| 功能 | 完整版 | 简化版 | 最简版 |
|------|--------|--------|--------|
| WebSocket 连接 | ✅ | ✅ | ✅ |
| 自动重连 | ✅ | ✅ | ✅ |
| 消息处理 | ✅ | ✅ | ✅ |
| 超时检测 | ✅ | ✅ | ✅ |
| 多服务器管理 | ✅ | ✅ | ✅ |
| 动态服务器列表 | ✅ | ❌ | ❌ |
| 数据库集成 | ✅ | ❌ | ❌ |
| Celery 集成 | ✅ | ❌ | ❌ |
| 完整错误处理 | ✅ | ✅ | 基础 |
| 详细日志 | ✅ | ✅ | 基础 |

## 📖 代码示例

### 最简版本 - 监控单个服务器
```python
import asyncio
import json
import websockets

async def simple_monitor():
    ws_url = "ws://*************:8188/ws?clientId=test"
    
    while True:
        try:
            async with websockets.connect(ws_url) as websocket:
                print("连接成功")
                async for message in websocket:
                    data = json.loads(message)
                    print(f"收到消息: {data.get('type')}")
        except Exception as e:
            print(f"错误: {e}")
            await asyncio.sleep(5)  # 5秒后重连

asyncio.run(simple_monitor())
```

### 监控多个服务器
```python
import asyncio
from minimal_websocket_monitor import monitor_multiple_servers

servers = [
    "*************:8188",
    "*************:8188",
    "*************:8188"
]

asyncio.run(monitor_multiple_servers(servers))
```

## 🔄 重连机制说明

### 触发重连的情况：
1. **网络断开** - 自动检测并重连
2. **服务器重启** - 连接断开后自动重连
3. **消息超时** - 60秒无消息时主动重连
4. **连接异常** - 任何连接错误都会触发重连

### 重连策略：
- **指数退避** - 重连间隔逐渐增加（5秒 → 10秒 → 20秒 → ...）
- **最大延迟** - 最长等待60秒
- **无限重试** - 除非手动停止，否则一直尝试重连

## 🛠️ 自定义配置

### 修改超时时间
```python
# 在 minimal_websocket_monitor.py 中修改
timeout = 120  # 改为120秒超时
```

### 修改重连延迟
```python
# 修改重连延迟策略
delay = min(10 * attempt, 120)  # 10秒递增，最大120秒
```

### 添加自定义消息处理
```python
async def custom_message_handler(data):
    msg_type = data.get('type')
    
    if msg_type == 'execution_start':
        # 任务开始时的自定义逻辑
        print("开始执行新任务")
        
    elif msg_type == 'executing':
        # 任务执行中的自定义逻辑
        node = data.get('data', {}).get('node')
        if node is None:
            print("任务完成")
```

## 🐛 常见问题

### Q: 如何停止监控？
A: 按 `Ctrl+C` 发送中断信号，程序会优雅退出

### Q: 如何添加更多服务器？
A: 在服务器列表中添加新的地址即可

### Q: 如何查看详细日志？
A: 修改日志级别为 DEBUG：
```python
logging.basicConfig(level=logging.DEBUG)
```

### Q: 连接失败怎么办？
A: 检查：
1. 服务器地址是否正确
2. ComfyUI 是否正在运行
3. 网络连接是否正常
4. 防火墙设置

## 📝 扩展建议

如果需要更多功能，可以考虑：

1. **添加数据库存储** - 保存消息到数据库
2. **添加 Web 界面** - 实时查看连接状态
3. **添加告警功能** - 连接异常时发送通知
4. **添加性能监控** - 监控消息处理性能
5. **添加配置文件** - 支持配置文件管理

## 🔗 相关链接

- [WebSockets 文档](https://websockets.readthedocs.io/)
- [AsyncIO 文档](https://docs.python.org/3/library/asyncio.html)
- [ComfyUI API 文档](https://github.com/comfyanonymous/ComfyUI)
