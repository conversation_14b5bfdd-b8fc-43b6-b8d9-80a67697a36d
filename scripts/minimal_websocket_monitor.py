#!/usr/bin/env python3
"""
最简化版 WebSocket 监控器
专注于核心功能：连接、监听、重连
"""

import asyncio
import json
import time
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger('MinimalWebSocketMonitor')

class MinimalWebSocketMonitor:
    """最简化的 WebSocket 监控器"""
    
    def __init__(self, server_address: str, client_id: str = "minimal_client"):
        self.server_address = server_address
        self.ws_url = f"ws://{server_address}/ws?clientId={client_id}"
        self.running = False
        self.last_message_time = time.time()
        
    async def start(self):
        """启动监控"""
        self.running = True
        attempt = 0
        
        while self.running:
            try:
                logger.info(f"连接到 {self.server_address} (尝试 {attempt + 1})")
                
                # 导入 websockets
                import websockets
                
                # 建立连接
                async with websockets.connect(self.ws_url, ping_interval=20) as websocket:
                    logger.info(f"✓ 已连接到 {self.server_address}")
                    attempt = 0  # 重置重试计数
                    self.last_message_time = time.time()
                    
                    # 启动超时监控
                    timeout_task = asyncio.create_task(self._timeout_monitor(websocket))
                    
                    try:
                        # 监听消息
                        async for message in websocket:
                            if not self.running:
                                break
                                
                            self.last_message_time = time.time()
                            await self._handle_message(message)
                            
                    finally:
                        timeout_task.cancel()
                        
            except Exception as e:
                logger.error(f"连接错误: {e}")
                if self.running:
                    attempt += 1
                    delay = min(5 * attempt, 60)  # 最大延迟60秒
                    logger.info(f"{delay}秒后重连...")
                    await asyncio.sleep(delay)
                    
        logger.info("监控已停止")
    
    async def _handle_message(self, message):
        """处理消息"""
        try:
            if isinstance(message, str):
                data = json.loads(message)
                msg_type = data.get('type', 'unknown')
                
                # 只记录重要消息
                if msg_type in ['execution_start', 'status', 'execution_error']:
                    logger.info(f"收到消息: {msg_type}")
                elif msg_type == 'executing':
                    node = data.get('data', {}).get('node')
                    if node is None:  # 任务完成
                        logger.info("任务执行完成")
                        
        except Exception as e:
            logger.warning(f"消息处理错误: {e}")
    
    async def _timeout_monitor(self, websocket):
        """超时监控"""
        timeout = 60  # 60秒超时
        
        while self.running:
            await asyncio.sleep(10)  # 每10秒检查一次
            
            if time.time() - self.last_message_time > timeout:
                logger.warning(f"{timeout}秒无消息，关闭连接重连")
                await websocket.close(code=1000, reason="超时重连")
                break
    
    def stop(self):
        """停止监控"""
        logger.info("正在停止监控...")
        self.running = False


async def simple_monitor_example():
    """简单使用示例"""
    # 创建监控器
    monitor = MinimalWebSocketMonitor("*************:8188")
    
    try:
        # 启动监控
        await monitor.start()
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    finally:
        # 停止监控
        monitor.stop()


# 更简单的函数式版本
async def simple_websocket_client(server_address: str):
    """
    最简单的 WebSocket 客户端函数
    
    Args:
        server_address: 服务器地址，如 "*************:8188"
    """
    import websockets
    
    ws_url = f"ws://{server_address}/ws?clientId=simple_client"
    
    while True:
        try:
            logger.info(f"连接到 {server_address}")
            
            async with websockets.connect(ws_url) as websocket:
                logger.info("连接成功，开始监听消息...")
                
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        msg_type = data.get('type')
                        
                        if msg_type == 'execution_start':
                            print(f"🚀 任务开始执行")
                        elif msg_type == 'executing':
                            node = data.get('data', {}).get('node')
                            if node is None:
                                print(f"✅ 任务执行完成")
                        elif msg_type == 'execution_error':
                            print(f"❌ 执行错误: {data}")
                        elif msg_type == 'status':
                            queue_info = data.get('data', {}).get('status', {}).get('exec_info', {})
                            queue_remaining = queue_info.get('queue_remaining', 0)
                            print(f"📊 队列剩余: {queue_remaining}")
                            
                    except json.JSONDecodeError:
                        pass  # 忽略非JSON消息
                        
        except Exception as e:
            logger.error(f"连接错误: {e}")
            logger.info("5秒后重连...")
            await asyncio.sleep(5)


# 批量监控多个服务器的简单版本
async def monitor_multiple_servers(servers: list):
    """
    监控多个服务器
    
    Args:
        servers: 服务器地址列表，如 ["*************:8188", "*************:8188"]
    """
    tasks = []
    
    # 为每个服务器创建监控任务
    for server in servers:
        task = asyncio.create_task(simple_websocket_client(server))
        tasks.append(task)
        logger.info(f"启动监控任务: {server}")
    
    try:
        # 等待所有任务
        await asyncio.gather(*tasks)
    except KeyboardInterrupt:
        logger.info("收到中断信号，停止所有监控")
        for task in tasks:
            task.cancel()


if __name__ == "__main__":
    # 使用示例
    
    # 方式1: 使用类
    # asyncio.run(simple_monitor_example())
    
    # 方式2: 使用简单函数监控单个服务器
    # asyncio.run(simple_websocket_client("*************:8188"))
    
    # 方式3: 监控多个服务器
    servers = [
        "*************:8188",
        "*************:8188",
        # 添加更多服务器...
    ]
    asyncio.run(monitor_multiple_servers(servers))
