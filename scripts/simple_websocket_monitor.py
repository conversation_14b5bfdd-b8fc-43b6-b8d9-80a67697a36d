#!/usr/bin/env python3
"""
简化版 WebSocket 监控器
功能：
1. 连接到 ComfyUI WebSocket 服务器
2. 监听消息并处理
3. 自动重连机制
4. 优雅关闭
"""

import asyncio
import json
import time
import random
import logging
from typing import Dict, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('SimpleWebSocketMonitor')

# 模拟配置
DEFAULT_CLIENT_ID = "simple_monitor_client"
RECONNECT_DELAY = 5  # 重连延迟（秒）
MESSAGE_TIMEOUT = 60  # 消息超时（秒）

class SimpleWebSocketMonitor:
    """简化的 WebSocket 监控器"""
    
    def __init__(self, server_address: str):
        """
        初始化监控器
        
        Args:
            server_address: ComfyUI 服务器地址，格式: "ip:port"
        """
        self.server_address = server_address
        self.ws_url = f"ws://{server_address}/ws?clientId={DEFAULT_CLIENT_ID}"
        self.is_running = False
        self.last_message_time = time.time()
        self.connected = False
        
    async def connect_and_monitor(self):
        """连接并监控 WebSocket"""
        self.is_running = True
        attempt = 0
        
        logger.info(f"Starting WebSocket monitor for {self.server_address}")
        
        while self.is_running:
            try:
                logger.info(f"Attempting to connect to {self.ws_url} (attempt {attempt + 1})")
                
                # 动态导入 websockets（避免启动时的依赖问题）
                try:
                    import websockets
                except ImportError:
                    logger.error("websockets library not found. Please install: pip install websockets")
                    return
                
                # 建立 WebSocket 连接
                async with websockets.connect(
                    self.ws_url, 
                    ping_interval=20, 
                    ping_timeout=10
                ) as websocket:
                    
                    self.connected = True
                    self.last_message_time = time.time()
                    attempt = 0  # 重置重试计数
                    
                    logger.info(f"Successfully connected to {self.server_address}")
                    
                    # 启动消息监控任务
                    monitor_task = asyncio.create_task(self._message_timeout_monitor(websocket))
                    
                    try:
                        # 监听消息
                        await self._listen_messages(websocket)
                    finally:
                        # 取消监控任务
                        monitor_task.cancel()
                        try:
                            await monitor_task
                        except asyncio.CancelledError:
                            pass
                        
            except Exception as e:
                self.connected = False
                logger.error(f"Connection error for {self.server_address}: {e}")
                
                if self.is_running:
                    attempt += 1
                    delay = min(RECONNECT_DELAY * (2 ** min(attempt, 5)), 60)  # 指数退避，最大60秒
                    logger.info(f"Reconnecting in {delay} seconds...")
                    await asyncio.sleep(delay)
                    
        logger.info(f"WebSocket monitor for {self.server_address} stopped")
    
    async def _listen_messages(self, websocket):
        """监听 WebSocket 消息"""
        try:
            async for message in websocket:
                if not self.is_running:
                    break
                    
                # 更新最后消息时间
                self.last_message_time = time.time()
                
                # 处理消息
                if isinstance(message, str):
                    await self._process_message(message)
                    
        except Exception as e:
            logger.error(f"Error listening to messages: {e}")
            raise
    
    async def _process_message(self, message: str):
        """处理接收到的消息"""
        try:
            data = json.loads(message)
            msg_type = data.get('type', 'unknown')
            
            # 记录重要消息
            if msg_type in ['execution_start', 'status', 'execution_error', 'executing']:
                logger.info(f"Received {msg_type} message from {self.server_address}")
                
                # 这里可以添加具体的消息处理逻辑
                # 例如：发送到消息队列、更新数据库等
                await self._handle_comfy_message(data)
                
        except json.JSONDecodeError:
            logger.warning(f"Invalid JSON message: {message[:100]}...")
        except Exception as e:
            logger.error(f"Error processing message: {e}")
    
    async def _handle_comfy_message(self, data: dict):
        """处理 ComfyUI 消息的具体逻辑"""
        msg_type = data.get('type')
        
        if msg_type == 'execution_start':
            logger.info(f"Task started on {self.server_address}")
            
        elif msg_type == 'status':
            queue_remaining = data.get('data', {}).get('status', {}).get('exec_info', {}).get('queue_remaining', 0)
            logger.info(f"Queue remaining on {self.server_address}: {queue_remaining}")
            
        elif msg_type == 'execution_error':
            logger.warning(f"Execution error on {self.server_address}: {data}")
            
        elif msg_type == 'executing':
            node = data.get('data', {}).get('node')
            if node is None:  # 任务完成
                logger.info(f"Task completed on {self.server_address}")
    
    async def _message_timeout_monitor(self, websocket):
        """监控消息超时"""
        while self.is_running:
            try:
                current_time = time.time()
                time_since_last_message = current_time - self.last_message_time
                
                # 检查是否超时
                if time_since_last_message > MESSAGE_TIMEOUT:
                    logger.warning(
                        f"No messages received from {self.server_address} for {MESSAGE_TIMEOUT} seconds, "
                        f"closing connection to trigger reconnect"
                    )
                    
                    # 主动关闭连接触发重连
                    await websocket.close(code=1000, reason="Message timeout")
                    return
                
                # 每10秒检查一次
                await asyncio.sleep(10)
                
            except Exception as e:
                logger.error(f"Error in timeout monitor: {e}")
                return
    
    def stop(self):
        """停止监控"""
        logger.info(f"Stopping WebSocket monitor for {self.server_address}")
        self.is_running = False
        self.connected = False


class SimpleMultiServerMonitor:
    """多服务器监控管理器"""
    
    def __init__(self):
        self.monitors: Dict[str, SimpleWebSocketMonitor] = {}
        self.tasks: Dict[str, asyncio.Task] = {}
        self.is_running = False
    
    def add_server(self, server_address: str):
        """添加服务器监控"""
        if server_address not in self.monitors:
            self.monitors[server_address] = SimpleWebSocketMonitor(server_address)
            logger.info(f"Added server: {server_address}")
    
    def remove_server(self, server_address: str):
        """移除服务器监控"""
        if server_address in self.monitors:
            # 停止监控
            self.monitors[server_address].stop()
            
            # 取消任务
            if server_address in self.tasks:
                self.tasks[server_address].cancel()
                del self.tasks[server_address]
            
            del self.monitors[server_address]
            logger.info(f"Removed server: {server_address}")
    
    async def start_all(self):
        """启动所有服务器监控"""
        self.is_running = True
        
        # 为每个服务器创建监控任务
        for address, monitor in self.monitors.items():
            task = asyncio.create_task(monitor.connect_and_monitor())
            self.tasks[address] = task
            logger.info(f"Started monitoring task for {address}")
        
        try:
            # 等待所有任务完成
            if self.tasks:
                await asyncio.gather(*self.tasks.values(), return_exceptions=True)
        except Exception as e:
            logger.error(f"Error in monitoring tasks: {e}")
    
    async def stop_all(self):
        """停止所有监控"""
        logger.info("Stopping all monitors...")
        self.is_running = False
        
        # 停止所有监控器
        for monitor in self.monitors.values():
            monitor.stop()
        
        # 取消所有任务
        for task in self.tasks.values():
            task.cancel()
        
        # 等待任务完成
        if self.tasks:
            await asyncio.gather(*self.tasks.values(), return_exceptions=True)
        
        self.tasks.clear()
        logger.info("All monitors stopped")
    
    def get_status(self) -> dict:
        """获取所有服务器的连接状态"""
        status = {}
        for address, monitor in self.monitors.items():
            status[address] = {
                'connected': monitor.connected,
                'last_message_time': monitor.last_message_time,
                'running': monitor.is_running
            }
        return status


async def main():
    """主函数示例"""
    # 创建多服务器监控器
    multi_monitor = SimpleMultiServerMonitor()
    
    # 添加服务器（示例地址）
    servers = [
        "*************:8188",
        "*************:8188",
        # 添加更多服务器...
    ]
    
    for server in servers:
        multi_monitor.add_server(server)
    
    try:
        # 启动监控
        await multi_monitor.start_all()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        # 优雅关闭
        await multi_monitor.stop_all()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
