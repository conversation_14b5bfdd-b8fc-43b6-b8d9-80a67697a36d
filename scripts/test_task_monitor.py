#!/usr/bin/env python3
"""
测试脚本：验证 task_monitor.py 的语法和基本结构
"""
import ast
import sys
import os

# 添加项目根目录到路径
current_file_path = os.path.abspath(__file__)
current_dir = os.path.dirname(current_file_path)
parent_dir = os.path.dirname(current_dir)
sys.path.append(parent_dir)


def test_syntax():
    """测试 task_monitor.py 的语法正确性"""
    print("Testing task_monitor.py syntax...")

    task_monitor_path = os.path.join(current_dir, 'task_monitor.py')

    try:
        with open(task_monitor_path, 'r', encoding='utf-8') as f:
            source_code = f.read()

        # 解析语法树
        ast.parse(source_code)
        print("✓ Syntax is valid")
        return True

    except SyntaxError as e:
        print(f"✗ Syntax error: {e}")
        return False
    except Exception as e:
        print(f"✗ Error reading file: {e}")
        return False


def test_function_definitions():
    """测试关键函数是否正确定义"""
    print("Testing function definitions...")

    task_monitor_path = os.path.join(current_dir, 'task_monitor.py')

    try:
        with open(task_monitor_path, 'r', encoding='utf-8') as f:
            source_code = f.read()

        # 检查关键函数是否存在
        required_functions = [
            'exponential_backoff',
            'work',
            'listen_to_websocket',
            'message_monitor',
            'refresh_comfy_servers',
            'get_connection_status_summary',
            'main'
        ]

        missing_functions = []
        for func_name in required_functions:
            if f"async def {func_name}" not in source_code and f"def {func_name}" not in source_code:
                missing_functions.append(func_name)

        if missing_functions:
            print(f"✗ Missing functions: {missing_functions}")
            return False
        else:
            print("✓ All required functions are defined")
            return True

    except Exception as e:
        print(f"✗ Error checking functions: {e}")
        return False


def test_global_variables():
    """测试全局变量是否正确定义"""
    print("Testing global variable definitions...")

    task_monitor_path = os.path.join(current_dir, 'task_monitor.py')

    try:
        with open(task_monitor_path, 'r', encoding='utf-8') as f:
            source_code = f.read()

        # 检查关键全局变量是否存在
        required_variables = [
            'connection_status',
            'active_websocket_tasks',
            'shutdown_events'
        ]

        missing_variables = []
        for var_name in required_variables:
            if f"{var_name}:" not in source_code and f"{var_name} =" not in source_code:
                missing_variables.append(var_name)

        if missing_variables:
            print(f"✗ Missing variables: {missing_variables}")
            return False
        else:
            print("✓ All required global variables are defined")
            return True

    except Exception as e:
        print(f"✗ Error checking variables: {e}")
        return False


def main():
    """运行所有测试"""
    print("Starting task_monitor validation tests...\n")

    tests = [
        test_syntax,
        test_function_definitions,
        test_global_variables
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1
        print()  # 空行分隔

    print(f"Results: {passed}/{total} tests passed")

    if passed == total:
        print("✓ All validation tests passed!")
        return 0
    else:
        print("✗ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
